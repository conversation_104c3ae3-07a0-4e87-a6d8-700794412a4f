import 'package:hive/hive.dart';
import '../utils/constants.dart';

part 'inspection_model.g.dart';

/// Modelo principal para una inspección de vehículo
@HiveType(typeId: 0)
class InspectionModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  DateTime inspectionDate;

  @HiveField(2)
  String agentName;

  @HiveField(3)
  String? insuredPersonName;

  @HiveField(4)
  DateTime? insuredPersonBirthDate;

  @HiveField(5)
  List<VehicleModel> vehicles;

  @HiveField(6)
  DateTime createdAt;

  @HiveField(7)
  DateTime updatedAt;

  @HiveField(8)
  bool isCompleted;

  @HiveField(9)
  String? pdfPath;

  InspectionModel({
    required this.id,
    required this.inspectionDate,
    required this.agentName,
    this.insuredPersonName,
    this.insuredPersonBirthDate,
    required this.vehicles,
    required this.createdAt,
    required this.updatedAt,
    this.isCompleted = false,
    this.pdfPath,
  });

  /// Genera un ID único para la inspección
  static String generateId(String personName, String vehicleModel, int year) {
    final cleanPersonName = personName.replaceAll(' ', '').toLowerCase();
    final cleanVehicleModel = vehicleModel.replaceAll(' ', '').toLowerCase();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${cleanPersonName}_${cleanVehicleModel}${year}_$timestamp';
  }

  /// Crea una nueva inspección
  factory InspectionModel.create({
    required String agentName,
    String? insuredPersonName,
    DateTime? insuredPersonBirthDate,
  }) {
    final now = DateTime.now();
    return InspectionModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      inspectionDate: now,
      agentName: agentName,
      insuredPersonName: insuredPersonName,
      insuredPersonBirthDate: insuredPersonBirthDate,
      vehicles: [],
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Agrega un vehículo a la inspección
  void addVehicle(VehicleModel vehicle) {
    vehicles.add(vehicle);
    updatedAt = DateTime.now();
    save();
  }

  /// Remueve un vehículo de la inspección
  void removeVehicle(String vehicleId) {
    vehicles.removeWhere((vehicle) => vehicle.id == vehicleId);
    updatedAt = DateTime.now();
    save();
  }

  /// Marca la inspección como completada
  void markAsCompleted(String pdfPath) {
    isCompleted = true;
    this.pdfPath = pdfPath;
    updatedAt = DateTime.now();
    save();
  }

  /// Convierte a Map para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'inspectionDate': inspectionDate.toIso8601String(),
      'agentName': agentName,
      'insuredPersonName': insuredPersonName,
      'insuredPersonBirthDate': insuredPersonBirthDate?.toIso8601String(),
      'vehicles': vehicles.map((v) => v.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isCompleted': isCompleted,
      'pdfPath': pdfPath,
    };
  }

  /// Crea desde Map JSON
  factory InspectionModel.fromJson(Map<String, dynamic> json) {
    return InspectionModel(
      id: json['id'],
      inspectionDate: DateTime.parse(json['inspectionDate']),
      agentName: json['agentName'],
      insuredPersonName: json['insuredPersonName'],
      insuredPersonBirthDate: json['insuredPersonBirthDate'] != null
          ? DateTime.parse(json['insuredPersonBirthDate'])
          : null,
      vehicles: (json['vehicles'] as List)
          .map((v) => VehicleModel.fromJson(v))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      isCompleted: json['isCompleted'] ?? false,
      pdfPath: json['pdfPath'],
    );
  }
}

/// Modelo para un vehículo en la inspección
@HiveType(typeId: 1)
class VehicleModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String brand;

  @HiveField(2)
  String model;

  @HiveField(3)
  int year;

  @HiveField(4)
  String inspectionId;

  @HiveField(5)
  List<PhotoModel> photos;

  @HiveField(6)
  DateTime createdAt;

  @HiveField(7)
  DateTime updatedAt;

  VehicleModel({
    required this.id,
    required this.brand,
    required this.model,
    required this.year,
    required this.inspectionId,
    required this.photos,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Crea un nuevo vehículo
  factory VehicleModel.create({
    required String brand,
    required String model,
    required int year,
    required String inspectionId,
  }) {
    final now = DateTime.now();
    return VehicleModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      brand: brand,
      model: model,
      year: year,
      inspectionId: inspectionId,
      photos: [],
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Agrega una foto al vehículo
  void addPhoto(PhotoModel photo) {
    photos.add(photo);
    updatedAt = DateTime.now();
    save();
  }

  /// Remueve una foto del vehículo
  void removePhoto(String photoId) {
    photos.removeWhere((photo) => photo.id == photoId);
    updatedAt = DateTime.now();
    save();
  }

  /// Obtiene fotos por ángulo
  List<PhotoModel> getPhotosByAngle(PhotoAngle angle) {
    return photos.where((photo) => photo.angle == angle).toList();
  }

  /// Verifica si tiene todas las fotos requeridas
  bool get hasAllRequiredPhotos {
    for (final angle in PhotoAngle.values) {
      if (angle.isRequired && getPhotosByAngle(angle).isEmpty) {
        return false;
      }
    }
    return true;
  }

  /// Nombre completo del vehículo
  String get fullName => '$brand $model $year';

  /// Convierte a Map para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'brand': brand,
      'model': model,
      'year': year,
      'inspectionId': inspectionId,
      'photos': photos.map((p) => p.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Crea desde Map JSON
  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      id: json['id'],
      brand: json['brand'],
      model: json['model'],
      year: json['year'],
      inspectionId: json['inspectionId'],
      photos: (json['photos'] as List)
          .map((p) => PhotoModel.fromJson(p))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

/// Modelo para una foto del vehículo
@HiveType(typeId: 2)
class PhotoModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String filePath;

  @HiveField(2)
  PhotoAngle angle;

  @HiveField(3)
  String vehicleId;

  @HiveField(4)
  DateTime capturedAt;

  @HiveField(5)
  String? description;

  PhotoModel({
    required this.id,
    required this.filePath,
    required this.angle,
    required this.vehicleId,
    required this.capturedAt,
    this.description,
  });

  /// Crea una nueva foto
  factory PhotoModel.create({
    required String filePath,
    required PhotoAngle angle,
    required String vehicleId,
    String? description,
  }) {
    return PhotoModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      filePath: filePath,
      angle: angle,
      vehicleId: vehicleId,
      capturedAt: DateTime.now(),
      description: description,
    );
  }

  /// Convierte a Map para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filePath': filePath,
      'angle': angle.name,
      'vehicleId': vehicleId,
      'capturedAt': capturedAt.toIso8601String(),
      'description': description,
    };
  }

  /// Crea desde Map JSON
  factory PhotoModel.fromJson(Map<String, dynamic> json) {
    return PhotoModel(
      id: json['id'],
      filePath: json['filePath'],
      angle: PhotoAngle.values.firstWhere((a) => a.name == json['angle']),
      vehicleId: json['vehicleId'],
      capturedAt: DateTime.parse(json['capturedAt']),
      description: json['description'],
    );
  }
}
