import 'package:flutter/material.dart';

/// Constantes de la aplicación SegurosInspectorApp
class AppConstants {
  // Información de la aplicación
  static const String appName = 'Seguros Inspector';
  static const String appVersion = '1.0.0';
  
  // Configuración de fotos
  static const int maxPhotosPerAngle = 2;
  static const double imageQuality = 0.8;
  
  // Configuración de PDF
  static const String pdfPrefix = 'Inspeccion_';
  static const String pdfExtension = '.pdf';
}

/// Paleta de colores corporativa
class AppColors {
  // Colores principales
  static const Color primary = Color(0xFF003366);           // Azul institucional
  static const Color secondary = Color(0xFF4D4D4D);         // Gris profesional
  static const Color background = Color(0xFFF9F9F9);        // Blanco suave
  static const Color accent = Color(0xFF007ACC);            // Azul claro moderno
  
  // Colores de estado
  static const Color success = Color(0xFF28A745);           // Verde profesional
  static const Color error = Color(0xDC3545);              // Rojo suave elegante
  static const Color warning = Color(0xFFFFC107);          // Amarillo advertencia
  
  // Colores adicionales
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF6C757D);
  static const Color lightGrey = Color(0xFFE9ECEF);
  static const Color darkGrey = Color(0xFF343A40);
  
  // Gradientes
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, accent],
  );
}

/// Estilos de texto corporativos
class AppTextStyles {
  // Títulos
  static const TextStyle headline1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
    letterSpacing: 0.5,
  );
  
  static const TextStyle headline2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
    letterSpacing: 0.3,
  );
  
  static const TextStyle headline3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.secondary,
    letterSpacing: 0.2,
  );
  
  // Subtítulos
  static const TextStyle subtitle1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.secondary,
    letterSpacing: 0.1,
  );
  
  static const TextStyle subtitle2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.grey,
    letterSpacing: 0.1,
  );
  
  // Texto del cuerpo
  static const TextStyle bodyText1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.secondary,
    height: 1.5,
  );
  
  static const TextStyle bodyText2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.grey,
    height: 1.4,
  );
  
  // Botones
  static const TextStyle button = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.white,
    letterSpacing: 0.5,
  );
  
  // Campos de texto
  static const TextStyle inputText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.secondary,
  );
  
  static const TextStyle inputLabel = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.grey,
  );
}

/// Dimensiones y espaciado
class AppDimensions {
  // Padding
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  // Margin
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  
  // Border radius
  static const double radiusS = 8.0;
  static const double radiusM = 16.0;
  static const double radiusL = 24.0;
  static const double radiusXL = 32.0;
  
  // Elevación
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  
  // Tamaños de iconos
  static const double iconS = 16.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
}

/// Ángulos de fotos requeridos
enum PhotoAngle {
  front('Frente'),
  back('Atrás'),
  leftSide('Lado Izquierdo'),
  rightSide('Lado Derecho'),
  vin('VIN Number'),
  registration('Registro del Vehículo'),
  ownerId('ID del Propietario'),
  location('Ubicación');

  const PhotoAngle(this.displayName);
  final String displayName;
  
  bool get isRequired => [front, back, leftSide, rightSide].contains(this);
  bool get isOptional => !isRequired;
}

/// Rutas de navegación
class AppRoutes {
  static const String home = '/';
  static const String form = '/form';
  static const String photoCapture = '/photo-capture';
  static const String summary = '/summary';
  static const String inspectionHistory = '/history';
}
